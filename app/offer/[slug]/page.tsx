import React from 'react';
import IndexClients from './index-clients';
import fetchWrapper from '@/utils/fetch-wrapper';
import {
  ContextOfferDealsType,
  GetOfferByIdResponse,
} from '@/services/api/data-contracts';
import { BASE_URL } from '@/config';
import { Metadata } from 'next';
import { generateProductSchema } from '@/utils/schema';

export async function generateMetadata({
  params,
}: {
  params: { slug?: string };
}): Promise<Metadata> {
  try {
    if (!params.slug) {
      return {
        title: 'Offer Not Found',
        description: 'The requested offer could not be found.',
      };
    }

    const result = await getOffersData(params.slug);

    return {
      title: `${result.offer?.offerTitle || 'Offer'} - IndianCashback.com`,
      description:
        result.offer?.offerDescription ||
        'Get the best deals and cashbacks with IndianCashback.com',
      alternates: {
        canonical: `https://www.indiancashback.com/offer/${encodeURIComponent(
          params.slug
        )}`,
      },
      openGraph: {
        url: `https://www.indiancashback.com/offer/${encodeURIComponent(
          params.slug
        )}`,
        title: result.offer?.offerTitle || 'Offer',
        description:
          result.offer?.offerDescription ||
          'Get the best deals and cashbacks with IndianCashback.com',
      },
    };
  } catch (err) {
    console.error(err);
    return {
      title: 'Offer Not Found',
      description: 'The requested offer could not be found.',
    };
  }
}

async function getOffersData(path: string = '') {
  console.log('path:', path);
  const res = await fetchWrapper<GetOfferByIdResponse>(
    `${BASE_URL}/offers/offer/title/${path}`,
    {
      cache: 'no-store',
    }
  );
  return res;
}

const Page = async ({ params }: { params: { slug?: string } }) => {
  let data: ContextOfferDealsType[] = [];
  let offerData: GetOfferByIdResponse | null = null;

  try {
    const result = await getOffersData(params?.slug);
    offerData = result;
    data = result?.similarOffers as unknown as ContextOfferDealsType[];
  } catch (error) {
    console.error('Failed to fetch offers:', error);
  }

  // Generate JSON-LD structured data for the offer as a product
  let offerSchema = null;
  if (offerData?.offer) {
    const offer = offerData.offer;

    // Format price for the offer
    const priceValue = offer.offerAmount ? offer.offerAmount.toString() : '';

    offerSchema = generateProductSchema({
      name: offer.offerTitle,
      description:
        offer.offerDescription ||
        `${offer.offerTitle} - Get the best deals and cashbacks`,
      image: offer.productImage,
      brand: {
        name: offer.storeName,
      },
      offers: {
        price: priceValue,
        priceCurrency: 'INR',
        url: `https://www.indiancashback.com/offer/${encodeURIComponent(
          params?.slug || ''
        )}`,
        ...(offer.endDate && { priceValidUntil: offer.endDate }),
      },
    });
  }

  return (
    <>
      {offerSchema && (
        <script
          dangerouslySetInnerHTML={{ __html: offerSchema }}
          type='application/ld+json'
        />
      )}
      <IndexClients data={data} />
    </>
  );
};

export default Page;
