import React from 'react';
import IndexClientsTMC from './index-clients';
import { BASE_URL } from '@/config';
import fetchWrapper from '@/utils/fetch-wrapper';
import { toast } from 'react-toastify';
import { Metadata } from 'next';
import { generateArticleSchema } from '@/utils/schema';

export const metadata: Metadata = {
  title: 'Privacy Policy - IndianCashback',
  description:
    'Learn how IndianCashback protects your personal information and data privacy. Our privacy policy explains what information we collect, how we use it, and the measures we take to keep your data secure while using our cashback services.',
  alternates: {
    canonical: 'https://www.indiancashback.com/privacy-policies',
  },
  openGraph: {
    url: 'https://www.indiancashback.com/privacy-policies',
    title: 'Privacy Policy - IndianCashback',
    description:
      'Learn how IndianCashback protects your personal information and data privacy. Our privacy policy explains what information we collect, how we use it, and the measures we take to keep your data secure while using our cashback services.',
  },
};

async function getPrivacyPoliciesData() {
  const res = await fetchWrapper<string>(
    `${BASE_URL}/context/terms-and-privacy?type=privacy`,
    {
      responseType: 'text',
    }
  );
  return res;
}

const Page = async () => {
  let resData: string;
  try {
    resData = await getPrivacyPoliciesData();
  } catch (err: any) {
    toast.error('Failed to fetch privacy policies data');
    console.log({ err });
    return err;
  }

  // Generate Article schema for the privacy policy
  const currentYear = new Date().getFullYear();
  const articleSchema = generateArticleSchema({
    title: 'Privacy Policies - IndianCashback.com',
    description:
      'Read the privacy policies of IndianCashback.com, one of the best cashback websites in India.',
    url: 'https://www.indiancashback.com/privacy-policies',
    authorName: 'IndianCashback Team',
    publisherName: 'IndianCashback.com',
    publisherLogo: 'https://www.indiancashback.com/img/logo.png',
    datePublished: `${currentYear}-01-01T00:00:00+05:30`, // Using the current year as the publication date
    dateModified: `${currentYear}-01-01T00:00:00+05:30`, // Using the current year as the modification date
    image: 'https://www.indiancashback.com/img/og-image.png',
  });

  return (
    <>
      <script
        dangerouslySetInnerHTML={{ __html: articleSchema }}
        type='application/ld+json'
      />
      <IndexClientsTMC data={resData} />
    </>
  );
};

export default Page;
