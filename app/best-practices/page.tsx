import React from 'react';
import { Metadata } from 'next';
import IndexClients from './index-clients';

export const metadata: Metadata = {
  title: 'Best Practices for Online Shopping - IndianCashback',
  description:
    'Learn the best practices for safe and rewarding online shopping. Discover expert tips on maximizing cashback, finding genuine deals, protecting your data, and making smarter purchase decisions.',
  alternates: {
    canonical: 'https://www.indiancashback.com/bestpractices',
  },
  openGraph: {
    url: 'https://www.indiancashback.com/bestpractices',
    title: 'Best Practices for Online Shopping - IndianCashback',
    description:
      'Learn the best practices for safe and rewarding online shopping. Discover expert tips on maximizing cashback, finding genuine deals, protecting your data, and making smarter purchase decisions.',
  },
};

const BestPracticesPage = () => {
  return <IndexClients />;
};

export default BestPracticesPage;
