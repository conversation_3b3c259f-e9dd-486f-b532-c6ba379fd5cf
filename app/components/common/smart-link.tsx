'use client';

import Link from 'next/link';
import {
  determineLinkType,
  getRelAttribute,
  LinkType,
} from '@/utils/link-utils';
import { forwardRef, ReactNode } from 'react';

interface SmartLinkProps {
  href: string;
  children: ReactNode;
  className?: string;
  linkType?: LinkType;
  target?: string;
  onClick?: (event?: React.MouseEvent<Element, MouseEvent>) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  ariaLabel?: string;
  title?: string;
}

/**
 * SmartLink component that automatically adds appropriate rel attributes based on link type
 *
 * @example
 * // Internal link (no rel attribute)
 * <SmartLink href="/about">About Us</SmartLink>
 *
 * @example
 * // External link (adds nofollow by default)
 * <SmartLink href="https://example.com">External Site</SmartLink>
 *
 * @example
 * // Sponsored link
 * <SmartLink href="https://partner.com" linkType={LinkType.EXTERNAL_SPONSORED}>Partner</SmartLink>
 */
const SmartLink = forwardRef<HTMLAnchorElement, SmartLinkProps>(
  ({
    href,
    children,
    className,
    linkType,
    target,
    onClick,
    onKeyDown,
    ariaLabel,
    title,
  }: SmartLinkProps) => {
    const type = determineLinkType(href, linkType);
    const rel = getRelAttribute(type);
    const isExternal = type !== LinkType.INTERNAL;

    // For external links, use anchor tag
    if (isExternal) {
      return (
        <a
          aria-label={ariaLabel}
          className={className}
          href={href}
          onClick={onClick}
          rel={rel}
          target={target || '_blank'}
          title={title}
        >
          {children}
        </a>
      );
    }

    // For internal links, use Next.js Link
    return (
      <Link
        aria-label={ariaLabel}
        className={className}
        href={href}
        onClick={onClick}
        onKeyDown={onKeyDown}
        target={target}
        title={title}
      >
        {children}
      </Link>
    );
    // eslint-disable-next-line react/display-name
  }
);

export default SmartLink;
