'use client';
import { useAppDispatch } from '@/redux/hooks';
import {
  setLoginModalClosable,
  setLoginModalOpen,
} from '@/redux/slices/auth-slice';
import React from 'react';
import LoginSignUpModal from '../auth/login-signup-modal';
import { usePathname } from 'next/navigation';
import { ProtectedPages } from '../../../static-data/constants';

const OpenAuthModal = ({ open }: { open: boolean }) => {
  const dispatch = useAppDispatch();
  const pathname = usePathname();

  if (!ProtectedPages.includes(pathname)) {
    return null;
  }
  if (open) {
    dispatch(setLoginModalOpen(true));
    dispatch(setLoginModalClosable(false));
  }
  return (
    <div className='py-5'>
      <LoginSignUpModal />
    </div>
  );
};

export default OpenAuthModal;
