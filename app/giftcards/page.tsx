// import IndexClientsGiftcards from './index-clients';
import { BASE_URL } from '@/config';
import {
  GetGiftCardListResponse,
  GiftCardBannersResponse,
  GiftCardControllerGetAllGiftCardsParams,
} from '@/services/api/data-contracts';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';

async function getAllGiftcardsData(
  searchParams: GiftCardControllerGetAllGiftCardsParams
) {
  const {
    searchParam = '',
    sortType = 'newest',
    subCategories = '',
    page = '1',
    pageSize = '15',
  } = {
    ...searchParams,
  };

  const accessToken = getCookie('accessToken', { cookies }) as string;
  return await fetchWrapper<GetGiftCardListResponse>(
    `${BASE_URL}/gift-cards?searchParam=${searchParam}&sortType=${sortType}&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}`,
    {
      token: accessToken,
      cache: 'no-store',
    }
  );
}

async function getGiftcardBanners() {
  return await fetchWrapper<GiftCardBannersResponse>(
    `${BASE_URL}/gift-cards/banners`
  );
}

const Page = async ({
  searchParams,
}: {
  searchParams: GiftCardControllerGetAllGiftCardsParams;
}) => {
  let resData: GetGiftCardListResponse;
  let resBanners: GiftCardBannersResponse;
  try {
    resData = await getAllGiftcardsData(searchParams); //eslint-disable-line
    resBanners = await getGiftcardBanners(); //eslint-disable-line
    // resData = {};
  } catch (err: any) {
    console.log({ err });
    return err;
  }
  return;
  // return <IndexClientsGiftcards banners={resBanners} data={resData} />;
};

export default Page;
export const dynamic = 'force-dynamic';
