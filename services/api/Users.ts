/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  GetBankAccountDataResponse,
  GetCbHistoryResponse,
  GetUserProfileResponseItem,
  GetUsersByReferralCodeResponse,
  OtpDto,
  UpdateBankAccountDto,
  UpdateCredentialsDto,
  UpdateProfileWithImageDto,
  UpdateUpiDto,
  UserControllerGetAllPersonalInterestDataData,
  UserControllerGetBankDetailsData,
  UserControllerGetCashbackHistoryData,
  UserControllerGetCashbackHistoryParams,
  UserControllerGetProfileDetailsData,
  UserControllerGetUsersByReferralCodeData,
  UserControllerGetUsersByReferralCodeParams,
  UserControllerGetUsersOverViewDetailsData,
  UserControllerSendOtpToUpdateUserCredentialsData,
  UserControllerUpdateAllUsersReferralCodeData,
  UserControllerUpdateBankDetailsData,
  UserControllerUpdateProfileData,
  UserControllerUpdateUpiIdData,
  UserControllerVerifyOtpToUpdateCredentialsData,
  UserListResponseDto,
  UserOverviewResponse,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Users<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags Users
   * @name UserControllerGetBankDetails
   * @request GET:/users/get-bank-details
   * @secure
   * @response `200` `UserControllerGetBankDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetBankAccountDataResponse`
   */
  userControllerGetBankDetails = (params: RequestParams = {}) =>
    this.request<
      UserControllerGetBankDetailsData,
      void | GetBankAccountDataResponse
    >({
      path: `/users/get-bank-details`,
      method: "GET",
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Users
   * @name UserControllerUpdateBankDetails
   * @request POST:/users/update-bank-details
   * @secure
   * @response `201` `UserControllerUpdateBankDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `boolean`
   */
  userControllerUpdateBankDetails = (
    data: UpdateBankAccountDto,
    params: RequestParams = {},
  ) =>
    this.request<UserControllerUpdateBankDetailsData, void | boolean>({
      path: `/users/update-bank-details`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Users
   * @name UserControllerUpdateUpiId
   * @request PATCH:/users/update-upi
   * @secure
   * @response `200` `UserControllerUpdateUpiIdData`
   * @response `401` `void` Unauthorized
   * @response `default` `boolean`
   */
  userControllerUpdateUpiId = (
    data: UpdateUpiDto,
    params: RequestParams = {},
  ) =>
    this.request<UserControllerUpdateUpiIdData, void | boolean>({
      path: `/users/update-upi`,
      method: "PATCH",
      body: data,
      secure: true,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Users
   * @name UserControllerGetProfileDetails
   * @request GET:/users/me
   * @secure
   * @response `200` `UserControllerGetProfileDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetUserProfileResponseItem`
   */
  userControllerGetProfileDetails = (params: RequestParams = {}) =>
    this.request<
      UserControllerGetProfileDetailsData,
      void | GetUserProfileResponseItem
    >({
      path: `/users/me`,
      method: "GET",
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Users
   * @name UserControllerGetUsersOverViewDetails
   * @request GET:/users/overview
   * @secure
   * @response `200` `UserControllerGetUsersOverViewDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `UserOverviewResponse`
   */
  userControllerGetUsersOverViewDetails = (params: RequestParams = {}) =>
    this.request<
      UserControllerGetUsersOverViewDetailsData,
      void | UserOverviewResponse
    >({
      path: `/users/overview`,
      method: "GET",
      secure: true,
      ...params,
    });
  /**
   * No description
   *
   * @tags Users
   * @name UserControllerGetUsersByReferralCode
   * @request GET:/users/referral-history
   * @secure
   * @response `200` `UserControllerGetUsersByReferralCodeData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetUsersByReferralCodeResponse`
   */
  userControllerGetUsersByReferralCode = (
    query: UserControllerGetUsersByReferralCodeParams,
    params: RequestParams = {},
  ) =>
    this.request<
      UserControllerGetUsersByReferralCodeData,
      void | GetUsersByReferralCodeResponse
    >({
      path: `/users/referral-history`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Users
   * @name UserControllerGetCashbackHistory
   * @request GET:/users/cashback-history
   * @secure
   * @response `200` `UserControllerGetCashbackHistoryData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetCbHistoryResponse`
   */
  userControllerGetCashbackHistory = (
    query: UserControllerGetCashbackHistoryParams,
    params: RequestParams = {},
  ) =>
    this.request<
      UserControllerGetCashbackHistoryData,
      void | GetCbHistoryResponse
    >({
      path: `/users/cashback-history`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Users
   * @name UserControllerUpdateProfile
   * @request POST:/users/update-profile
   * @secure
   * @response `201` `UserControllerUpdateProfileData`
   * @response `401` `void` Unauthorized
   * @response `default` `boolean`
   */
  userControllerUpdateProfile = (
    data: UpdateProfileWithImageDto,
    params: RequestParams = {},
  ) =>
    this.request<UserControllerUpdateProfileData, void | boolean>({
      path: `/users/update-profile`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.FormData,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Users
   * @name UserControllerSendOtpToUpdateUserCredentials
   * @request POST:/users/update-credentials
   * @secure
   * @response `201` `UserControllerSendOtpToUpdateUserCredentialsData`
   * @response `401` `void` Unauthorized
   */
  userControllerSendOtpToUpdateUserCredentials = (
    data: UpdateCredentialsDto,
    params: RequestParams = {},
  ) =>
    this.request<UserControllerSendOtpToUpdateUserCredentialsData, void>({
      path: `/users/update-credentials`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      ...params,
    });
  /**
   * No description
   *
   * @tags Users
   * @name UserControllerVerifyOtpToUpdateCredentials
   * @request POST:/users/verify-credentials-otp
   * @secure
   * @response `201` `UserControllerVerifyOtpToUpdateCredentialsData`
   * @response `401` `void` Unauthorized
   */
  userControllerVerifyOtpToUpdateCredentials = (
    data: OtpDto,
    params: RequestParams = {},
  ) =>
    this.request<UserControllerVerifyOtpToUpdateCredentialsData, void>({
      path: `/users/verify-credentials-otp`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Users
   * @name UserControllerGetAllPersonalInterestData
   * @request GET:/users/get-personal-interest
   * @response `200` `UserControllerGetAllPersonalInterestDataData`
   */
  userControllerGetAllPersonalInterestData = (params: RequestParams = {}) =>
    this.request<UserControllerGetAllPersonalInterestDataData, any>({
      path: `/users/get-personal-interest`,
      method: "GET",
      ...params,
    });
  /**
   * No description
   *
   * @tags Users
   * @name UserControllerUpdateAllUsersReferralCode
   * @request POST:/users/update-referral-code
   * @response `201` `UserControllerUpdateAllUsersReferralCodeData`
   */
  userControllerUpdateAllUsersReferralCode = (params: RequestParams = {}) =>
    this.request<UserControllerUpdateAllUsersReferralCodeData, any>({
      path: `/users/update-referral-code`,
      method: "POST",
      ...params,
    });
  /**
   * No description
   *
   * @tags Users
   * @name UserControllerListUsers
   * @request GET:/users/list
   * @response `default` `UserListResponseDto`
   */
  userControllerListUsers = (params: RequestParams = {}) =>
    this.request<any, UserListResponseDto>({
      path: `/users/list`,
      method: "GET",
      ...params,
    });
}
