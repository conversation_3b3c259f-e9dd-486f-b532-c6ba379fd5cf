/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  AuthControllerCheckData,
  AuthControllerCreateData,
  AuthControllerGetCsrfTokenData,
  AuthControllerGoogleAuthCallbackData,
  AuthControllerGoogleAuthData,
  AuthControllerLoginData,
  AuthControllerLogoutData,
  AuthControllerResendOtpData,
  AuthControllerVerifyOtpData,
  AuthControllerVerifyTokenData,
  CreateUserDto,
  LoginDto,
  VerifyUserDto,
} from "./data-contracts";

export namespace Auth {
  /**
   * No description
   * @tags Auth
   * @name AuthControllerCreate
   * @request POST:/auth/register
   * @response `201` `AuthControllerCreateData`
   * @response `default` `UserResponse`
   */
  export namespace AuthControllerCreate {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = CreateUserDto;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerCreateData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerLogin
   * @request POST:/auth/login
   * @response `201` `AuthControllerLoginData`
   * @response `default` `LoginResponse`
   */
  export namespace AuthControllerLogin {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = LoginDto;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerLoginData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerVerifyOtp
   * @request POST:/auth/verify-user
   * @response `201` `AuthControllerVerifyOtpData`
   * @response `default` `boolean`
   */
  export namespace AuthControllerVerifyOtp {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = VerifyUserDto;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerVerifyOtpData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerResendOtp
   * @request POST:/auth/resend-otp
   * @response `201` `AuthControllerResendOtpData`
   * @response `default` `LoginResponse`
   */
  export namespace AuthControllerResendOtp {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = LoginDto;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerResendOtpData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerLogout
   * @request DELETE:/auth/logout
   * @secure
   * @response `200` `AuthControllerLogoutData`
   * @response `401` `void` Unauthorized
   */
  export namespace AuthControllerLogout {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerLogoutData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerCheck
   * @request GET:/auth/check
   * @secure
   * @response `200` `AuthControllerCheckData`
   * @response `401` `void` Unauthorized
   */
  export namespace AuthControllerCheck {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerCheckData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerGetCsrfToken
   * @request GET:/auth/token
   * @response `200` `AuthControllerGetCsrfTokenData`
   */
  export namespace AuthControllerGetCsrfToken {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerGetCsrfTokenData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerGoogleAuth
   * @request GET:/auth/google
   * @response `200` `AuthControllerGoogleAuthData`
   * @response `default` `void` Initiates Google OAuth authentication flow
   */
  export namespace AuthControllerGoogleAuth {
    export type RequestParams = {};
    export type RequestQuery = {
      referralCode: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerGoogleAuthData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerGoogleAuthCallback
   * @request GET:/auth/google/callback
   * @response `200` `AuthControllerGoogleAuthCallbackData`
   * @response `default` `void` Handles the callback from Google OAuth authentication
   */
  export namespace AuthControllerGoogleAuthCallback {
    export type RequestParams = {};
    export type RequestQuery = {
      redirect: string;
      state: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerGoogleAuthCallbackData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerVerifyToken
   * @request GET:/auth/verify-token
   * @response `200` `AuthControllerVerifyTokenData`
   */
  export namespace AuthControllerVerifyToken {
    export type RequestParams = {};
    export type RequestQuery = {
      token: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerVerifyTokenData;
  }
}
