/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  CashbackSortTypes,
  OtpDto,
  ReferralTypes,
  UpdateBankAccountDto,
  UpdateCredentialsDto,
  UpdateProfileWithImageDto,
  UpdateUpiDto,
  UserControllerGetAllPersonalInterestDataData,
  UserControllerGetBankDetailsData,
  UserControllerGetCashbackHistoryData,
  UserControllerGetCashbackHistoryParams1StatusEnum,
  UserControllerGetProfileDetailsData,
  UserControllerGetUsersByReferralCodeData,
  UserControllerGetUsersByReferralCodeParams1StatusEnum,
  UserControllerGetUsersOverViewDetailsData,
  UserControllerSendOtpToUpdateUserCredentialsData,
  UserControllerUpdateAllUsersReferralCodeData,
  UserControllerUpdateBankDetailsData,
  UserControllerUpdateProfileData,
  UserControllerUpdateUpiIdData,
  UserControllerVerifyOtpToUpdateCredentialsData,
} from "./data-contracts";

export namespace Users {
  /**
   * No description
   * @tags Users
   * @name UserControllerGetBankDetails
   * @request GET:/users/get-bank-details
   * @secure
   * @response `200` `UserControllerGetBankDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetBankAccountDataResponse`
   */
  export namespace UserControllerGetBankDetails {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerGetBankDetailsData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerUpdateBankDetails
   * @request POST:/users/update-bank-details
   * @secure
   * @response `201` `UserControllerUpdateBankDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `boolean`
   */
  export namespace UserControllerUpdateBankDetails {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = UpdateBankAccountDto;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerUpdateBankDetailsData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerUpdateUpiId
   * @request PATCH:/users/update-upi
   * @secure
   * @response `200` `UserControllerUpdateUpiIdData`
   * @response `401` `void` Unauthorized
   * @response `default` `boolean`
   */
  export namespace UserControllerUpdateUpiId {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = UpdateUpiDto;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerUpdateUpiIdData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerGetProfileDetails
   * @request GET:/users/me
   * @secure
   * @response `200` `UserControllerGetProfileDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetUserProfileResponseItem`
   */
  export namespace UserControllerGetProfileDetails {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerGetProfileDetailsData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerGetUsersOverViewDetails
   * @request GET:/users/overview
   * @secure
   * @response `200` `UserControllerGetUsersOverViewDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `UserOverviewResponse`
   */
  export namespace UserControllerGetUsersOverViewDetails {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerGetUsersOverViewDetailsData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerGetUsersByReferralCode
   * @request GET:/users/referral-history
   * @secure
   * @response `200` `UserControllerGetUsersByReferralCodeData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetUsersByReferralCodeResponse`
   */
  export namespace UserControllerGetUsersByReferralCode {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @example "2021-10-10" */
      endDate?: string;
      /** @example "a" */
      searchParam?: string;
      sortType?: ReferralTypes;
      /** @example "2021-10-10" */
      startDate?: string;
      /** @default "active" */
      status?: UserControllerGetUsersByReferralCodeParams1StatusEnum;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerGetUsersByReferralCodeData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerGetCashbackHistory
   * @request GET:/users/cashback-history
   * @secure
   * @response `200` `UserControllerGetCashbackHistoryData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetCbHistoryResponse`
   */
  export namespace UserControllerGetCashbackHistory {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @default "2024-12-31" */
      endDate: string;
      /** @example "a" */
      searchParam: string;
      sortType?: CashbackSortTypes;
      /** @default "2023-10-01" */
      startDate: string;
      /** @example "cancelled,pending,confirmed" */
      status?: UserControllerGetCashbackHistoryParams1StatusEnum;
      /** @example "1,2,3" */
      stores?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerGetCashbackHistoryData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerUpdateProfile
   * @request POST:/users/update-profile
   * @secure
   * @response `201` `UserControllerUpdateProfileData`
   * @response `401` `void` Unauthorized
   * @response `default` `boolean`
   */
  export namespace UserControllerUpdateProfile {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = UpdateProfileWithImageDto;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerUpdateProfileData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerSendOtpToUpdateUserCredentials
   * @request POST:/users/update-credentials
   * @secure
   * @response `201` `UserControllerSendOtpToUpdateUserCredentialsData`
   * @response `401` `void` Unauthorized
   */
  export namespace UserControllerSendOtpToUpdateUserCredentials {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = UpdateCredentialsDto;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerSendOtpToUpdateUserCredentialsData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerVerifyOtpToUpdateCredentials
   * @request POST:/users/verify-credentials-otp
   * @secure
   * @response `201` `UserControllerVerifyOtpToUpdateCredentialsData`
   * @response `401` `void` Unauthorized
   */
  export namespace UserControllerVerifyOtpToUpdateCredentials {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = OtpDto;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerVerifyOtpToUpdateCredentialsData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerGetAllPersonalInterestData
   * @request GET:/users/get-personal-interest
   * @response `200` `UserControllerGetAllPersonalInterestDataData`
   */
  export namespace UserControllerGetAllPersonalInterestData {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerGetAllPersonalInterestDataData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerUpdateAllUsersReferralCode
   * @request POST:/users/update-referral-code
   * @response `201` `UserControllerUpdateAllUsersReferralCodeData`
   */
  export namespace UserControllerUpdateAllUsersReferralCode {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerUpdateAllUsersReferralCodeData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerListUsers
   * @request GET:/users/list
   * @response `default` `UserListResponseDto`
   */
  export namespace UserControllerListUsers {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = any;
  }
}
